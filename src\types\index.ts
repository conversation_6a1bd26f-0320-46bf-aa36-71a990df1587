// 用戶相關類型
export interface User {
  id: string;
  line_user_id: string;
  name: string;
  avatar_url?: string;
  created_at: string;
  last_login: string;
}

// 服務人員類型
export interface Staff {
  id: string;
  name: string;
  avatar_url?: string;
  is_active: boolean;
  created_at: string;
}

// 服務類別類型
export interface ServiceCategory {
  id: string;
  name: string;
  sort_order: number;
  is_active: boolean;
}

// 服務項目類型
export interface Service {
  id: string;
  category_id: string;
  name: string;
  duration_minutes: number;
  price: number;
  image_url?: string;
  is_active: boolean;
  category?: ServiceCategory;
}

// 人員排班類型
export interface StaffSchedule {
  id: string;
  staff_id: string;
  working_date: string;
  start_time: string;
  end_time: string;
  staff?: Staff;
}

// 預約狀態枚舉
export enum BookingStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  NO_SHOW = 'no_show'
}

// 預約類型
export interface Booking {
  id: string;
  user_id: string;
  service_id: string;
  staff_id: string;
  booking_datetime: string;
  status: BookingStatus;
  paid_amount: number;
  created_at: string;
  user?: User;
  service?: Service;
  staff?: Staff;
}

// 票券類型枚舉
export enum VoucherType {
  DISCOUNT = 'discount',
  FREE_SERVICE = 'free_service',
  CASH_VOUCHER = 'cash_voucher'
}

// 票券類型
export interface Voucher {
  id: string;
  user_id: string;
  voucher_code: string;
  type: VoucherType;
  value: number;
  expiry_date: string;
  is_used: boolean;
  used_at?: string;
  booking_id?: string;
}

// 儲值金類型
export interface StoredValue {
  id: string;
  user_id: string;
  balance: number;
  updated_at: string;
}

// 儲值金交易類型
export interface StoredValueTransaction {
  id: string;
  user_id: string;
  amount: number;
  type: 'deposit' | 'withdraw';
  description: string;
  created_at: string;
}

// LINE 配置類型
export interface LineConfig {
  id: string;
  channel_id: string;
  channel_secret: string;
  channel_access_token: string;
  updated_at: string;
}

// API 響應類型
export interface ApiResponse<T> {
  data: T;
  error?: string;
  message?: string;
}

// 分頁類型
export interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

// 分頁響應類型
export interface PaginatedResponse<T> {
  data: T[];
  pagination: Pagination;
}

// 預約時段類型
export interface TimeSlot {
  time: string;
  available: boolean;
  staff_id?: string;
}

// 預約表單類型
export interface BookingForm {
  category_id: string;
  service_id: string;
  staff_id: string;
  booking_date: string;
  booking_time: string;
  use_voucher?: string;
  use_stored_value?: number;
}

// 載入狀態類型
export interface LoadingState {
  [key: string]: boolean;
}

// 錯誤狀態類型
export interface ErrorState {
  [key: string]: string | null;
}
