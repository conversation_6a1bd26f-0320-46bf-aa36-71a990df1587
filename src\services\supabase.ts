import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});

// 資料庫表名常數
export const TABLES = {
  USERS: 'users',
  STAFF: 'staff',
  SERVICE_CATEGORIES: 'service_categories',
  SERVICES: 'services',
  STAFF_SCHEDULES: 'staff_schedules',
  BOOKINGS: 'bookings',
  VOUCHERS: 'vouchers',
  STORED_VALUES: 'stored_values',
  STORED_VALUE_TRANSACTIONS: 'stored_value_transactions',
  LINE_CONFIG: 'line_config'
} as const;

// 儲存桶名稱常數
export const STORAGE_BUCKETS = {
  AVATARS: 'avatars',
  SERVICE_IMAGES: 'service-images',
  STAFF_IMAGES: 'staff-images'
} as const;
