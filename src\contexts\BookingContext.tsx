import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { ServiceCategory, Service, Staff, BookingForm } from '../types';

// 預約狀態介面
interface BookingState {
  // 選擇的項目
  selectedCategory: ServiceCategory | null;
  selectedService: Service | null;
  selectedStaff: Staff | null;
  selectedDate: string | null;
  selectedTime: string | null;
  
  // 表單資料
  bookingForm: Partial<BookingForm>;
  
  // 載入狀態
  loading: {
    categories: boolean;
    services: boolean;
    staff: boolean;
    timeSlots: boolean;
    booking: boolean;
  };
  
  // 錯誤狀態
  errors: {
    categories: string | null;
    services: string | null;
    staff: string | null;
    timeSlots: string | null;
    booking: string | null;
  };
  
  // 資料
  categories: ServiceCategory[];
  services: Service[];
  staffList: Staff[];
  availableTimeSlots: string[];
}

// 動作類型
type BookingAction =
  | { type: 'SET_LOADING'; payload: { key: keyof BookingState['loading']; loading: boolean } }
  | { type: 'SET_ERROR'; payload: { key: keyof BookingState['errors']; error: string | null } }
  | { type: 'SET_CATEGORIES'; payload: ServiceCategory[] }
  | { type: 'SET_SERVICES'; payload: Service[] }
  | { type: 'SET_STAFF_LIST'; payload: Staff[] }
  | { type: 'SET_TIME_SLOTS'; payload: string[] }
  | { type: 'SELECT_CATEGORY'; payload: ServiceCategory }
  | { type: 'SELECT_SERVICE'; payload: Service }
  | { type: 'SELECT_STAFF'; payload: Staff }
  | { type: 'SELECT_DATE'; payload: string }
  | { type: 'SELECT_TIME'; payload: string }
  | { type: 'UPDATE_FORM'; payload: Partial<BookingForm> }
  | { type: 'RESET_BOOKING' }
  | { type: 'RESET_FROM_STEP'; payload: 'category' | 'service' | 'staff' | 'datetime' };

// 初始狀態
const initialState: BookingState = {
  selectedCategory: null,
  selectedService: null,
  selectedStaff: null,
  selectedDate: null,
  selectedTime: null,
  bookingForm: {},
  loading: {
    categories: false,
    services: false,
    staff: false,
    timeSlots: false,
    booking: false,
  },
  errors: {
    categories: null,
    services: null,
    staff: null,
    timeSlots: null,
    booking: null,
  },
  categories: [],
  services: [],
  staffList: [],
  availableTimeSlots: [],
};

// Reducer 函數
function bookingReducer(state: BookingState, action: BookingAction): BookingState {
  switch (action.type) {
    case 'SET_LOADING':
      return {
        ...state,
        loading: {
          ...state.loading,
          [action.payload.key]: action.payload.loading,
        },
      };

    case 'SET_ERROR':
      return {
        ...state,
        errors: {
          ...state.errors,
          [action.payload.key]: action.payload.error,
        },
      };

    case 'SET_CATEGORIES':
      return {
        ...state,
        categories: action.payload,
      };

    case 'SET_SERVICES':
      return {
        ...state,
        services: action.payload,
      };

    case 'SET_STAFF_LIST':
      return {
        ...state,
        staffList: action.payload,
      };

    case 'SET_TIME_SLOTS':
      return {
        ...state,
        availableTimeSlots: action.payload,
      };

    case 'SELECT_CATEGORY':
      return {
        ...state,
        selectedCategory: action.payload,
        selectedService: null,
        selectedStaff: null,
        selectedDate: null,
        selectedTime: null,
        services: [],
        staffList: [],
        availableTimeSlots: [],
        bookingForm: {
          ...state.bookingForm,
          category_id: action.payload.id,
        },
      };

    case 'SELECT_SERVICE':
      return {
        ...state,
        selectedService: action.payload,
        selectedStaff: null,
        selectedDate: null,
        selectedTime: null,
        staffList: [],
        availableTimeSlots: [],
        bookingForm: {
          ...state.bookingForm,
          service_id: action.payload.id,
        },
      };

    case 'SELECT_STAFF':
      return {
        ...state,
        selectedStaff: action.payload,
        selectedDate: null,
        selectedTime: null,
        availableTimeSlots: [],
        bookingForm: {
          ...state.bookingForm,
          staff_id: action.payload.id,
        },
      };

    case 'SELECT_DATE':
      return {
        ...state,
        selectedDate: action.payload,
        selectedTime: null,
        availableTimeSlots: [],
        bookingForm: {
          ...state.bookingForm,
          booking_date: action.payload,
        },
      };

    case 'SELECT_TIME':
      return {
        ...state,
        selectedTime: action.payload,
        bookingForm: {
          ...state.bookingForm,
          booking_time: action.payload,
        },
      };

    case 'UPDATE_FORM':
      return {
        ...state,
        bookingForm: {
          ...state.bookingForm,
          ...action.payload,
        },
      };

    case 'RESET_BOOKING':
      return initialState;

    case 'RESET_FROM_STEP':
      switch (action.payload) {
        case 'category':
          return {
            ...state,
            selectedCategory: null,
            selectedService: null,
            selectedStaff: null,
            selectedDate: null,
            selectedTime: null,
            services: [],
            staffList: [],
            availableTimeSlots: [],
            bookingForm: {},
          };
        case 'service':
          return {
            ...state,
            selectedService: null,
            selectedStaff: null,
            selectedDate: null,
            selectedTime: null,
            staffList: [],
            availableTimeSlots: [],
            bookingForm: {
              category_id: state.bookingForm.category_id,
            },
          };
        case 'staff':
          return {
            ...state,
            selectedStaff: null,
            selectedDate: null,
            selectedTime: null,
            availableTimeSlots: [],
            bookingForm: {
              category_id: state.bookingForm.category_id,
              service_id: state.bookingForm.service_id,
            },
          };
        case 'datetime':
          return {
            ...state,
            selectedDate: null,
            selectedTime: null,
            availableTimeSlots: [],
            bookingForm: {
              category_id: state.bookingForm.category_id,
              service_id: state.bookingForm.service_id,
              staff_id: state.bookingForm.staff_id,
            },
          };
        default:
          return state;
      }

    default:
      return state;
  }
}

// Context 介面
interface BookingContextType {
  state: BookingState;
  setLoading: (key: keyof BookingState['loading'], loading: boolean) => void;
  setError: (key: keyof BookingState['errors'], error: string | null) => void;
  setCategories: (categories: ServiceCategory[]) => void;
  setServices: (services: Service[]) => void;
  setStaffList: (staff: Staff[]) => void;
  setTimeSlots: (timeSlots: string[]) => void;
  selectCategory: (category: ServiceCategory) => void;
  selectService: (service: Service) => void;
  selectStaff: (staff: Staff) => void;
  selectDate: (date: string) => void;
  selectTime: (time: string) => void;
  updateForm: (formData: Partial<BookingForm>) => void;
  resetBooking: () => void;
  resetFromStep: (step: 'category' | 'service' | 'staff' | 'datetime') => void;
  isStepComplete: (step: 'category' | 'service' | 'staff' | 'datetime') => boolean;
  canProceedToNext: (currentStep: 'category' | 'service' | 'staff' | 'datetime') => boolean;
}

// 建立 Context
const BookingContext = createContext<BookingContextType | undefined>(undefined);

// Provider 組件
interface BookingProviderProps {
  children: ReactNode;
}

export const BookingProvider: React.FC<BookingProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(bookingReducer, initialState);

  const setLoading = (key: keyof BookingState['loading'], loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: { key, loading } });
  };

  const setError = (key: keyof BookingState['errors'], error: string | null) => {
    dispatch({ type: 'SET_ERROR', payload: { key, error } });
  };

  const setCategories = (categories: ServiceCategory[]) => {
    dispatch({ type: 'SET_CATEGORIES', payload: categories });
  };

  const setServices = (services: Service[]) => {
    dispatch({ type: 'SET_SERVICES', payload: services });
  };

  const setStaffList = (staff: Staff[]) => {
    dispatch({ type: 'SET_STAFF_LIST', payload: staff });
  };

  const setTimeSlots = (timeSlots: string[]) => {
    dispatch({ type: 'SET_TIME_SLOTS', payload: timeSlots });
  };

  const selectCategory = (category: ServiceCategory) => {
    dispatch({ type: 'SELECT_CATEGORY', payload: category });
  };

  const selectService = (service: Service) => {
    dispatch({ type: 'SELECT_SERVICE', payload: service });
  };

  const selectStaff = (staff: Staff) => {
    dispatch({ type: 'SELECT_STAFF', payload: staff });
  };

  const selectDate = (date: string) => {
    dispatch({ type: 'SELECT_DATE', payload: date });
  };

  const selectTime = (time: string) => {
    dispatch({ type: 'SELECT_TIME', payload: time });
  };

  const updateForm = (formData: Partial<BookingForm>) => {
    dispatch({ type: 'UPDATE_FORM', payload: formData });
  };

  const resetBooking = () => {
    dispatch({ type: 'RESET_BOOKING' });
  };

  const resetFromStep = (step: 'category' | 'service' | 'staff' | 'datetime') => {
    dispatch({ type: 'RESET_FROM_STEP', payload: step });
  };

  const isStepComplete = (step: 'category' | 'service' | 'staff' | 'datetime'): boolean => {
    switch (step) {
      case 'category':
        return !!state.selectedCategory;
      case 'service':
        return !!state.selectedService;
      case 'staff':
        return !!state.selectedStaff;
      case 'datetime':
        return !!state.selectedDate && !!state.selectedTime;
      default:
        return false;
    }
  };

  const canProceedToNext = (currentStep: 'category' | 'service' | 'staff' | 'datetime'): boolean => {
    return isStepComplete(currentStep);
  };

  const value: BookingContextType = {
    state,
    setLoading,
    setError,
    setCategories,
    setServices,
    setStaffList,
    setTimeSlots,
    selectCategory,
    selectService,
    selectStaff,
    selectDate,
    selectTime,
    updateForm,
    resetBooking,
    resetFromStep,
    isStepComplete,
    canProceedToNext,
  };

  return (
    <BookingContext.Provider value={value}>
      {children}
    </BookingContext.Provider>
  );
};

// Hook 來使用 Context
export const useBooking = (): BookingContextType => {
  const context = useContext(BookingContext);
  if (context === undefined) {
    throw new Error('useBooking must be used within a BookingProvider');
  }
  return context;
};
