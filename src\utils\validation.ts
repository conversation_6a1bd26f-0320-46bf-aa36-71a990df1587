/**
 * 驗證工具函數
 * 提供各種資料驗證功能
 */

/**
 * 驗證電子郵件格式
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 驗證台灣手機號碼格式
 */
export function isValidPhoneNumber(phone: string): boolean {
  const phoneRegex = /^09\d{8}$/;
  const cleaned = phone.replace(/\D/g, '');
  return phoneRegex.test(cleaned);
}

/**
 * 驗證密碼強度
 */
export function validatePassword(password: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('密碼長度至少需要 8 個字符');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('密碼需要包含至少一個大寫字母');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('密碼需要包含至少一個小寫字母');
  }
  
  if (!/\d/.test(password)) {
    errors.push('密碼需要包含至少一個數字');
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('密碼需要包含至少一個特殊字符');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * 驗證必填欄位
 */
export function isRequired(value: any): boolean {
  if (value === null || value === undefined) {
    return false;
  }
  
  if (typeof value === 'string') {
    return value.trim().length > 0;
  }
  
  if (Array.isArray(value)) {
    return value.length > 0;
  }
  
  return true;
}

/**
 * 驗證字串長度
 */
export function validateLength(
  value: string,
  min?: number,
  max?: number
): { isValid: boolean; error?: string } {
  const length = value.length;
  
  if (min !== undefined && length < min) {
    return {
      isValid: false,
      error: `長度不能少於 ${min} 個字符`,
    };
  }
  
  if (max !== undefined && length > max) {
    return {
      isValid: false,
      error: `長度不能超過 ${max} 個字符`,
    };
  }
  
  return { isValid: true };
}

/**
 * 驗證數字範圍
 */
export function validateRange(
  value: number,
  min?: number,
  max?: number
): { isValid: boolean; error?: string } {
  if (min !== undefined && value < min) {
    return {
      isValid: false,
      error: `值不能小於 ${min}`,
    };
  }
  
  if (max !== undefined && value > max) {
    return {
      isValid: false,
      error: `值不能大於 ${max}`,
    };
  }
  
  return { isValid: true };
}

/**
 * 驗證日期格式
 */
export function isValidDate(dateString: string): boolean {
  const date = new Date(dateString);
  return !isNaN(date.getTime());
}

/**
 * 驗證時間格式 (HH:MM)
 */
export function isValidTime(timeString: string): boolean {
  const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
  return timeRegex.test(timeString);
}

/**
 * 驗證 URL 格式
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * 驗證圖片檔案類型
 */
export function isValidImageFile(file: File): boolean {
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  return validTypes.includes(file.type);
}

/**
 * 驗證檔案大小
 */
export function validateFileSize(file: File, maxSizeMB: number): boolean {
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  return file.size <= maxSizeBytes;
}

/**
 * 表單驗證器
 */
export class FormValidator {
  private errors: Record<string, string[]> = {};
  
  /**
   * 驗證欄位
   */
  validate(field: string, value: any, rules: ValidationRule[]): this {
    this.errors[field] = [];
    
    for (const rule of rules) {
      const result = rule.validator(value);
      if (!result.isValid) {
        this.errors[field].push(result.error || '驗證失敗');
      }
    }
    
    if (this.errors[field].length === 0) {
      delete this.errors[field];
    }
    
    return this;
  }
  
  /**
   * 檢查是否有錯誤
   */
  hasErrors(): boolean {
    return Object.keys(this.errors).length > 0;
  }
  
  /**
   * 獲取所有錯誤
   */
  getErrors(): Record<string, string[]> {
    return this.errors;
  }
  
  /**
   * 獲取特定欄位的錯誤
   */
  getFieldErrors(field: string): string[] {
    return this.errors[field] || [];
  }
  
  /**
   * 清除所有錯誤
   */
  clearErrors(): this {
    this.errors = {};
    return this;
  }
}

/**
 * 驗證規則介面
 */
export interface ValidationRule {
  validator: (value: any) => { isValid: boolean; error?: string };
}

/**
 * 常用驗證規則
 */
export const ValidationRules = {
  required: (): ValidationRule => ({
    validator: (value) => ({
      isValid: isRequired(value),
      error: '此欄位為必填',
    }),
  }),
  
  email: (): ValidationRule => ({
    validator: (value) => ({
      isValid: !value || isValidEmail(value),
      error: '請輸入有效的電子郵件地址',
    }),
  }),
  
  phone: (): ValidationRule => ({
    validator: (value) => ({
      isValid: !value || isValidPhoneNumber(value),
      error: '請輸入有效的手機號碼',
    }),
  }),
  
  minLength: (min: number): ValidationRule => ({
    validator: (value) => validateLength(value || '', min),
  }),
  
  maxLength: (max: number): ValidationRule => ({
    validator: (value) => validateLength(value || '', undefined, max),
  }),
  
  range: (min?: number, max?: number): ValidationRule => ({
    validator: (value) => validateRange(Number(value), min, max),
  }),
};
