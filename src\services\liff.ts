import type { Liff } from '@liff/liff-types';

declare global {
  interface Window {
    liff: Liff;
  }
}

const LIFF_ID = import.meta.env.VITE_LIFF_ID;

export class LiffService {
  private static instance: LiffService;
  private liff: Liff | null = null;
  private initialized = false;

  private constructor() {}

  static getInstance(): LiffService {
    if (!LiffService.instance) {
      LiffService.instance = new LiffService();
    }
    return LiffService.instance;
  }

  /**
   * 初始化 LIFF
   */
  async init(): Promise<void> {
    if (this.initialized) return;

    try {
      // 載入 LIFF SDK
      await this.loadLiffSDK();
      
      if (!window.liff) {
        throw new Error('LIFF SDK not loaded');
      }

      this.liff = window.liff;

      // 初始化 LIFF
      await this.liff.init({ liffId: LIFF_ID });
      this.initialized = true;

      console.log('LIFF initialized successfully');
    } catch (error) {
      console.error('Failed to initialize LIFF:', error);
      throw error;
    }
  }

  /**
   * 載入 LIFF SDK
   */
  private loadLiffSDK(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (window.liff) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://static.line-scdn.net/liff/edge/2/sdk.js';
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load LIFF SDK'));
      document.head.appendChild(script);
    });
  }

  /**
   * 檢查是否在 LINE 環境中
   */
  isInClient(): boolean {
    if (!this.liff) return false;
    return this.liff.isInClient();
  }

  /**
   * 檢查用戶是否已登入
   */
  isLoggedIn(): boolean {
    if (!this.liff) return false;
    return this.liff.isLoggedIn();
  }

  /**
   * 登入
   */
  async login(): Promise<void> {
    if (!this.liff) {
      throw new Error('LIFF not initialized');
    }

    if (this.isInClient()) {
      // 在 LINE 應用內，直接登入
      this.liff.login();
    } else {
      // 在外部瀏覽器，重定向到 LINE 登入頁面
      this.liff.login({
        redirectUri: window.location.href
      });
    }
  }

  /**
   * 登出
   */
  logout(): void {
    if (!this.liff) return;
    this.liff.logout();
  }

  /**
   * 獲取用戶資料
   */
  async getProfile() {
    if (!this.liff || !this.isLoggedIn()) {
      throw new Error('User not logged in');
    }

    try {
      const profile = await this.liff.getProfile();
      return {
        userId: profile.userId,
        displayName: profile.displayName,
        pictureUrl: profile.pictureUrl,
        statusMessage: profile.statusMessage
      };
    } catch (error) {
      console.error('Failed to get profile:', error);
      throw error;
    }
  }

  /**
   * 獲取 Access Token
   */
  getAccessToken(): string | null {
    if (!this.liff || !this.isLoggedIn()) {
      return null;
    }
    return this.liff.getAccessToken();
  }

  /**
   * 獲取 ID Token
   */
  getIDToken(): string | null {
    if (!this.liff || !this.isLoggedIn()) {
      return null;
    }
    return this.liff.getIDToken();
  }

  /**
   * 關閉 LIFF 視窗
   */
  closeWindow(): void {
    if (!this.liff) return;
    this.liff.closeWindow();
  }

  /**
   * 發送訊息給用戶
   */
  async sendMessages(messages: any[]): Promise<void> {
    if (!this.liff || !this.isInClient()) {
      throw new Error('Can only send messages in LINE client');
    }

    try {
      await this.liff.sendMessages(messages);
    } catch (error) {
      console.error('Failed to send messages:', error);
      throw error;
    }
  }

  /**
   * 分享目標選擇器
   */
  async shareTargetPicker(messages: any[]): Promise<void> {
    if (!this.liff || !this.isInClient()) {
      throw new Error('Can only use share target picker in LINE client');
    }

    try {
      await this.liff.shareTargetPicker(messages);
    } catch (error) {
      console.error('Failed to open share target picker:', error);
      throw error;
    }
  }

  /**
   * 獲取環境資訊
   */
  getContext() {
    if (!this.liff) return null;
    return this.liff.getContext();
  }

  /**
   * 檢查是否為開發環境
   */
  isDevelopment(): boolean {
    return import.meta.env.VITE_DEV_MODE === 'true';
  }
}

// 導出單例實例
export const liffService = LiffService.getInstance();
