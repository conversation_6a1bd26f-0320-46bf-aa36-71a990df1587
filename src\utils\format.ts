/**
 * 格式化工具函數
 * 處理各種資料格式化需求
 */

/**
 * 格式化金額為新台幣格式
 */
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('zh-TW', {
    style: 'currency',
    currency: 'TWD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
}

/**
 * 格式化數字為千分位格式
 */
export function formatNumber(num: number): string {
  return new Intl.NumberFormat('zh-TW').format(num);
}

/**
 * 格式化電話號碼
 */
export function formatPhoneNumber(phone: string): string {
  // 移除所有非數字字符
  const cleaned = phone.replace(/\D/g, '');
  
  // 台灣手機號碼格式 (09XX-XXX-XXX)
  if (cleaned.length === 10 && cleaned.startsWith('09')) {
    return `${cleaned.slice(0, 4)}-${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
  }
  
  // 台灣市話格式 (0X-XXXX-XXXX)
  if (cleaned.length === 9 || cleaned.length === 10) {
    if (cleaned.length === 9) {
      return `${cleaned.slice(0, 2)}-${cleaned.slice(2, 6)}-${cleaned.slice(6)}`;
    } else {
      return `${cleaned.slice(0, 2)}-${cleaned.slice(2, 6)}-${cleaned.slice(6)}`;
    }
  }
  
  return phone;
}

/**
 * 截斷文字並添加省略號
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) {
    return text;
  }
  return text.slice(0, maxLength) + '...';
}

/**
 * 格式化檔案大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 格式化百分比
 */
export function formatPercentage(value: number, total: number): string {
  if (total === 0) return '0%';
  const percentage = (value / total) * 100;
  return `${percentage.toFixed(1)}%`;
}

/**
 * 格式化時長（分鐘轉換為小時分鐘）
 */
export function formatDuration(minutes: number): string {
  if (minutes < 60) {
    return `${minutes} 分鐘`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (remainingMinutes === 0) {
    return `${hours} 小時`;
  }
  
  return `${hours} 小時 ${remainingMinutes} 分鐘`;
}

/**
 * 格式化評分星級
 */
export function formatRating(rating: number): string {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;
  
  let stars = '★'.repeat(fullStars);
  if (hasHalfStar) {
    stars += '☆';
  }
  stars += '☆'.repeat(5 - Math.ceil(rating));
  
  return `${stars} (${rating.toFixed(1)})`;
}

/**
 * 格式化預約狀態
 */
export function formatBookingStatus(status: string): { text: string; color: string } {
  const statusMap: Record<string, { text: string; color: string }> = {
    pending: { text: '待確認', color: 'text-yellow-600 bg-yellow-100' },
    confirmed: { text: '已確認', color: 'text-green-600 bg-green-100' },
    completed: { text: '已完成', color: 'text-blue-600 bg-blue-100' },
    cancelled: { text: '已取消', color: 'text-red-600 bg-red-100' },
    no_show: { text: '未到場', color: 'text-gray-600 bg-gray-100' },
  };
  
  return statusMap[status] || { text: '未知', color: 'text-gray-600 bg-gray-100' };
}

/**
 * 格式化票券類型
 */
export function formatVoucherType(type: string): string {
  const typeMap: Record<string, string> = {
    discount: '折扣券',
    free_service: '免費服務券',
    cash_voucher: '現金券',
  };
  
  return typeMap[type] || '未知類型';
}

/**
 * 移除 HTML 標籤
 */
export function stripHtml(html: string): string {
  const tmp = document.createElement('div');
  tmp.innerHTML = html;
  return tmp.textContent || tmp.innerText || '';
}

/**
 * 首字母大寫
 */
export function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * 駝峰命名轉換為短橫線命名
 */
export function camelToKebab(str: string): string {
  return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase();
}

/**
 * 短橫線命名轉換為駝峰命名
 */
export function kebabToCamel(str: string): string {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
}
