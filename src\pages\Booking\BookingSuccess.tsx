import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Layout from '../../components/Layout/Layout';

const BookingSuccess: React.FC = () => {
  const { bookingId } = useParams<{ bookingId: string }>();
  const navigate = useNavigate();

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-md mx-auto px-4 py-6">
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🎉</div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">預約成功！</h1>
            <p className="text-gray-600 mb-6">預約編號: {bookingId}</p>
            <p className="text-gray-600 mb-8">您的預約已成功建立，我們會盡快為您安排服務。</p>
            
            <div className="space-y-3">
              <button
                onClick={() => navigate('/bookings')}
                className="w-full bg-primary-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-primary-600 transition-colors"
              >
                查看我的預約
              </button>
              <button
                onClick={() => navigate('/')}
                className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors"
              >
                回到首頁
              </button>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default BookingSuccess;
