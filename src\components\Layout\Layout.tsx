import React, { ReactNode } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../UI/LoadingSpinner';

interface LayoutProps {
  children: ReactNode;
  showHeader?: boolean;
  showFooter?: boolean;
  className?: string;
}

const Layout: React.FC<LayoutProps> = ({ 
  children, 
  showHeader = true, 
  showFooter = false,
  className = '' 
}) => {
  const { loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center beauty-gradient">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <div className={`min-h-screen flex flex-col ${className}`}>
      {showHeader && <Header />}
      
      <main className="flex-1">
        {children}
      </main>
      
      {showFooter && <Footer />}
    </div>
  );
};

/**
 * 頁面標題組件
 */
const Header: React.FC = () => {
  const { user } = useAuth();

  return (
    <header className="bg-white shadow-sm border-b border-gray-100">
      <div className="max-w-md mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {user?.avatar_url && (
              <img
                src={user.avatar_url}
                alt={user.name}
                className="w-8 h-8 rounded-full"
              />
            )}
            <div>
              <h1 className="text-lg font-semibold text-gray-900">
                {import.meta.env.VITE_APP_NAME}
              </h1>
              {user && (
                <p className="text-sm text-gray-500">
                  歡迎，{user.name}
                </p>
              )}
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* 通知按鈕 */}
            <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M9 11h.01M9 8h.01M6 20.01V8a2 2 0 012-2h8a2 2 0 012 2v12.01" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};

/**
 * 頁腳組件
 */
const Footer: React.FC = () => {
  return (
    <footer className="bg-white border-t border-gray-100 py-4">
      <div className="max-w-md mx-auto px-4 text-center">
        <p className="text-sm text-gray-500">
          © 2024 {import.meta.env.VITE_APP_NAME}. All rights reserved.
        </p>
      </div>
    </footer>
  );
};

export default Layout;
