import { supabase, TABLES } from './supabase';
import {
  User,
  Staff,
  ServiceCategory,
  Service,
  StaffSchedule,
  Booking,
  Voucher,
  StoredValue,
  StoredValueTransaction,
  BookingStatus,
  ApiResponse,
  PaginatedResponse,
} from '../types';

/**
 * API 服務類別
 * 提供與 Supabase 資料庫互動的方法
 */
export class ApiService {
  /**
   * 用戶相關 API
   */
  static users = {
    /**
     * 根據 LINE User ID 獲取用戶
     */
    async getByLineUserId(lineUserId: string): Promise<User | null> {
      const { data, error } = await supabase
        .from(TABLES.USERS)
        .select('*')
        .eq('line_user_id', lineUserId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null; // 找不到記錄
        throw new Error(error.message);
      }

      return data;
    },

    /**
     * 建立新用戶
     */
    async create(userData: Omit<User, 'id' | 'created_at'>): Promise<User> {
      const { data, error } = await supabase
        .from(TABLES.USERS)
        .insert(userData)
        .select()
        .single();

      if (error) throw new Error(error.message);
      return data;
    },

    /**
     * 更新用戶資料
     */
    async update(id: string, updates: Partial<User>): Promise<User> {
      const { data, error } = await supabase
        .from(TABLES.USERS)
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw new Error(error.message);
      return data;
    },
  };

  /**
   * 服務類別相關 API
   */
  static categories = {
    /**
     * 獲取所有啟用的服務類別
     */
    async getAll(): Promise<ServiceCategory[]> {
      const { data, error } = await supabase
        .from(TABLES.SERVICE_CATEGORIES)
        .select('*')
        .eq('is_active', true)
        .order('sort_order');

      if (error) throw new Error(error.message);
      return data || [];
    },

    /**
     * 根據 ID 獲取服務類別
     */
    async getById(id: string): Promise<ServiceCategory | null> {
      const { data, error } = await supabase
        .from(TABLES.SERVICE_CATEGORIES)
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null;
        throw new Error(error.message);
      }

      return data;
    },
  };

  /**
   * 服務項目相關 API
   */
  static services = {
    /**
     * 根據類別 ID 獲取服務項目
     */
    async getByCategory(categoryId: string): Promise<Service[]> {
      const { data, error } = await supabase
        .from(TABLES.SERVICES)
        .select(`
          *,
          category:service_categories(*)
        `)
        .eq('category_id', categoryId)
        .eq('is_active', true);

      if (error) throw new Error(error.message);
      return data || [];
    },

    /**
     * 根據 ID 獲取服務項目
     */
    async getById(id: string): Promise<Service | null> {
      const { data, error } = await supabase
        .from(TABLES.SERVICES)
        .select(`
          *,
          category:service_categories(*)
        `)
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null;
        throw new Error(error.message);
      }

      return data;
    },

    /**
     * 獲取所有啟用的服務項目
     */
    async getAll(): Promise<Service[]> {
      const { data, error } = await supabase
        .from(TABLES.SERVICES)
        .select(`
          *,
          category:service_categories(*)
        `)
        .eq('is_active', true);

      if (error) throw new Error(error.message);
      return data || [];
    },
  };

  /**
   * 服務人員相關 API
   */
  static staff = {
    /**
     * 獲取所有啟用的服務人員
     */
    async getAll(): Promise<Staff[]> {
      const { data, error } = await supabase
        .from(TABLES.STAFF)
        .select('*')
        .eq('is_active', true);

      if (error) throw new Error(error.message);
      return data || [];
    },

    /**
     * 根據服務 ID 獲取可提供該服務的人員
     */
    async getByService(serviceId: string): Promise<Staff[]> {
      // 這裡需要根據實際的業務邏輯來實作
      // 暫時返回所有啟用的人員
      return this.getAll();
    },

    /**
     * 根據 ID 獲取服務人員
     */
    async getById(id: string): Promise<Staff | null> {
      const { data, error } = await supabase
        .from(TABLES.STAFF)
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null;
        throw new Error(error.message);
      }

      return data;
    },
  };

  /**
   * 排班相關 API
   */
  static schedules = {
    /**
     * 獲取人員在特定日期的排班
     */
    async getByStaffAndDate(staffId: string, date: string): Promise<StaffSchedule[]> {
      const { data, error } = await supabase
        .from(TABLES.STAFF_SCHEDULES)
        .select(`
          *,
          staff:staff(*)
        `)
        .eq('staff_id', staffId)
        .eq('working_date', date);

      if (error) throw new Error(error.message);
      return data || [];
    },

    /**
     * 獲取人員在日期範圍內的排班
     */
    async getByStaffAndDateRange(
      staffId: string,
      startDate: string,
      endDate: string
    ): Promise<StaffSchedule[]> {
      const { data, error } = await supabase
        .from(TABLES.STAFF_SCHEDULES)
        .select(`
          *,
          staff:staff(*)
        `)
        .eq('staff_id', staffId)
        .gte('working_date', startDate)
        .lte('working_date', endDate);

      if (error) throw new Error(error.message);
      return data || [];
    },
  };

  /**
   * 預約相關 API
   */
  static bookings = {
    /**
     * 建立新預約
     */
    async create(bookingData: Omit<Booking, 'id' | 'created_at' | 'updated_at'>): Promise<Booking> {
      const { data, error } = await supabase
        .from(TABLES.BOOKINGS)
        .insert(bookingData)
        .select(`
          *,
          user:users(*),
          service:services(*),
          staff:staff(*)
        `)
        .single();

      if (error) throw new Error(error.message);
      return data;
    },

    /**
     * 獲取用戶的預約記錄
     */
    async getByUser(userId: string, limit = 20, offset = 0): Promise<PaginatedResponse<Booking>> {
      const { data, error, count } = await supabase
        .from(TABLES.BOOKINGS)
        .select(`
          *,
          user:users(*),
          service:services(*),
          staff:staff(*)
        `, { count: 'exact' })
        .eq('user_id', userId)
        .order('booking_datetime', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) throw new Error(error.message);

      return {
        data: data || [],
        pagination: {
          page: Math.floor(offset / limit) + 1,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit),
        },
      };
    },

    /**
     * 根據 ID 獲取預約
     */
    async getById(id: string): Promise<Booking | null> {
      const { data, error } = await supabase
        .from(TABLES.BOOKINGS)
        .select(`
          *,
          user:users(*),
          service:services(*),
          staff:staff(*)
        `)
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null;
        throw new Error(error.message);
      }

      return data;
    },

    /**
     * 更新預約狀態
     */
    async updateStatus(id: string, status: BookingStatus): Promise<Booking> {
      const { data, error } = await supabase
        .from(TABLES.BOOKINGS)
        .update({ status })
        .eq('id', id)
        .select(`
          *,
          user:users(*),
          service:services(*),
          staff:staff(*)
        `)
        .single();

      if (error) throw new Error(error.message);
      return data;
    },

    /**
     * 取消預約
     */
    async cancel(id: string): Promise<Booking> {
      return this.updateStatus(id, BookingStatus.CANCELLED);
    },
  };

  /**
   * 票券相關 API
   */
  static vouchers = {
    /**
     * 獲取用戶的票券
     */
    async getByUser(userId: string): Promise<Voucher[]> {
      const { data, error } = await supabase
        .from(TABLES.VOUCHERS)
        .select('*')
        .eq('user_id', userId)
        .eq('is_used', false)
        .gte('expiry_date', new Date().toISOString().split('T')[0]);

      if (error) throw new Error(error.message);
      return data || [];
    },

    /**
     * 使用票券
     */
    async use(id: string, bookingId: string): Promise<Voucher> {
      const { data, error } = await supabase
        .from(TABLES.VOUCHERS)
        .update({
          is_used: true,
          used_at: new Date().toISOString(),
          booking_id: bookingId,
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw new Error(error.message);
      return data;
    },
  };

  /**
   * 儲值金相關 API
   */
  static storedValue = {
    /**
     * 獲取用戶儲值金餘額
     */
    async getBalance(userId: string): Promise<number> {
      const { data, error } = await supabase
        .from(TABLES.STORED_VALUES)
        .select('balance')
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return 0; // 沒有記錄時返回 0
        throw new Error(error.message);
      }

      return data.balance;
    },

    /**
     * 獲取用戶儲值金交易記錄
     */
    async getTransactions(userId: string, limit = 20, offset = 0): Promise<PaginatedResponse<StoredValueTransaction>> {
      const { data, error, count } = await supabase
        .from(TABLES.STORED_VALUE_TRANSACTIONS)
        .select('*', { count: 'exact' })
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) throw new Error(error.message);

      return {
        data: data || [],
        pagination: {
          page: Math.floor(offset / limit) + 1,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit),
        },
      };
    },
  };
}
