import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User } from '../types';
import { supabase } from '../services/supabase';
import { liffService } from '../services/liff';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  login: () => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  /**
   * 初始化認證狀態
   */
  useEffect(() => {
    initializeAuth();
  }, []);

  /**
   * 初始化認證
   */
  const initializeAuth = async () => {
    try {
      setLoading(true);
      setError(null);

      // 初始化 LIFF
      await liffService.init();

      // 檢查 LIFF 登入狀態
      if (liffService.isLoggedIn()) {
        await handleLiffLogin();
      } else {
        // 檢查 Supabase 會話
        const { data: { session } } = await supabase.auth.getSession();
        if (session?.user) {
          await loadUserProfile(session.user.id);
        }
      }
    } catch (err) {
      console.error('Auth initialization error:', err);
      setError(err instanceof Error ? err.message : '認證初始化失敗');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 處理 LIFF 登入
   */
  const handleLiffLogin = async () => {
    try {
      // 獲取 LINE 用戶資料
      const lineProfile = await liffService.getProfile();
      const accessToken = liffService.getAccessToken();

      if (!lineProfile || !accessToken) {
        throw new Error('無法獲取 LINE 用戶資料');
      }

      // 檢查用戶是否已存在
      let { data: existingUser, error: fetchError } = await supabase
        .from('users')
        .select('*')
        .eq('line_user_id', lineProfile.userId)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') {
        throw fetchError;
      }

      if (!existingUser) {
        // 建立新用戶
        const { data: newUser, error: createError } = await supabase
          .from('users')
          .insert({
            line_user_id: lineProfile.userId,
            name: lineProfile.displayName,
            avatar_url: lineProfile.pictureUrl,
            last_login: new Date().toISOString()
          })
          .select()
          .single();

        if (createError) throw createError;
        existingUser = newUser;
      } else {
        // 更新最後登入時間
        const { error: updateError } = await supabase
          .from('users')
          .update({ 
            last_login: new Date().toISOString(),
            name: lineProfile.displayName,
            avatar_url: lineProfile.pictureUrl
          })
          .eq('id', existingUser.id);

        if (updateError) throw updateError;
      }

      // 使用 LINE Access Token 登入 Supabase
      const { error: signInError } = await supabase.auth.signInWithIdToken({
        provider: 'line',
        token: accessToken
      });

      if (signInError) {
        console.warn('Supabase sign in failed, continuing with LINE auth:', signInError);
      }

      setUser(existingUser);
    } catch (err) {
      console.error('LIFF login error:', err);
      throw err;
    }
  };

  /**
   * 載入用戶資料
   */
  const loadUserProfile = async (userId: string) => {
    try {
      const { data: userData, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) throw error;
      setUser(userData);
    } catch (err) {
      console.error('Load user profile error:', err);
      throw err;
    }
  };

  /**
   * 登入
   */
  const login = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!liffService.isLoggedIn()) {
        await liffService.login();
      } else {
        await handleLiffLogin();
      }
    } catch (err) {
      console.error('Login error:', err);
      setError(err instanceof Error ? err.message : '登入失敗');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  /**
   * 登出
   */
  const logout = async () => {
    try {
      setLoading(true);
      
      // 登出 Supabase
      await supabase.auth.signOut();
      
      // 登出 LIFF
      liffService.logout();
      
      setUser(null);
    } catch (err) {
      console.error('Logout error:', err);
      setError(err instanceof Error ? err.message : '登出失敗');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 重新載入用戶資料
   */
  const refreshUser = async () => {
    if (!user) return;
    
    try {
      await loadUserProfile(user.id);
    } catch (err) {
      console.error('Refresh user error:', err);
      setError(err instanceof Error ? err.message : '重新載入用戶資料失敗');
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    error,
    login,
    logout,
    refreshUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

/**
 * 使用認證上下文的 Hook
 */
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
